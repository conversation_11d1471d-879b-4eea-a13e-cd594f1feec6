<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import { STATUS_OPTIONS } from '.';
  import { convertIsoTimeToLocalTime, copyText, debounce, deepEqual } from '@/utils/common';
  import { EditOutlined, DeleteOutlined, FileTextOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { useRouter, useRoute } from 'vue-router';
  import { createAgent, fetchAgentList, publishAgent, delAgent, updateAgentPublish } from '@/api/agent.ts';
  import type { IPage } from '@/interface';
  import type { IAgentItem } from '@/interface/agent';
  import { message } from 'ant-design-vue';
  const router = useRouter();
  const route = useRoute();
  const DEFAULT_SEARCH_STATE = {
    name: '',
    status: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCH_STATE });
  const spinning = ref(false);
  const delVisible = ref(false);
  const containerRef = ref();
  const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 9,
    defaultPageSize: 9,
    showTotal: (total: number) => `共 ${total} 条数据`,
    showSizeChanger: false,
  });
  const pageParame: IPage = reactive({ page: 1, limit: 9 });
  const dataSource = ref<IAgentItem[]>([]);
  const currentRecord = reactive<{
    id: string;
    name: string;
  }>({
    id: '',
    name: '',
  });
  const handleAdd = async () => {
    try {
      const data = await createAgent();
      router.push(`/intelligent/${data.id}`);
    } catch (e) {
      message.warn(`创建失败：${e}`);
    }
  };

  const onSearch = () => {
    Object.assign(pagination, { current: 1, pageSize: 9 });
    Object.assign(pageParame, { page: 1, limit: 9 });
    fetchList();
  };
  const debouncedSearch = debounce(onSearch);
  const fetchList = async () => {
    spinning.value = true;
    const data: { items: IAgentItem[]; total: number } = await fetchAgentList({
      ...pageParame,
      ...searchState,
    });
    const { items, total } = data;
    pagination.total = total;
    dataSource.value = items;
    spinning.value = false;
  };
  const handlePublish = async (record: IAgentItem) => {
    const { id, status, deploy_id, publish_channel, origins } = record;
    // 接口调用成功后再跳转
    router.push(`/intelligent/${id}?tab=publish`);

    if (status !== 'archived') {
      if (!deploy_id) {
        message.warn('模型未配置，请配置后再发布');
        return;
      }
      try {
        if (publish_channel.length > 0) {
          await updateAgentPublish(id as string, {
            publish_channel: publish_channel,
            origins: origins,
          });
          message.success('发布成功');
        } else {
          await publishAgent(id);
          message.success('发布成功');
        }
      } catch (error) {
        console.error('发布失败:', error);
        message.warn('发布失败，请联系管理员');
      }
    } else {
      return;
    }
  };
  const handleDelete = (record: IAgentItem) => {
    const { id, name } = record;
    Object.assign(currentRecord, { id, name });
    delVisible.value = true;
  };

  const confirmDelete = async () => {
    await delAgent(currentRecord.id);
    delVisible.value = false;
    message.success('已删除');
    fetchList();
  };

  const handleToggle = (page: number, pageSize: number) => {
    Object.assign(pagination, { current: page, pageSize });
    Object.assign(pageParame, { page, limit: pageSize });
    fetchList();
  };

  console.log(location.href, 'location.href  index');
  const handlePreview = (master_id: string) => {
    const href = `${location.href}/preview/${master_id}`;
    window.open(href);
  };
  const handleCopy = (master_id: string) => {
    const href = `${location.href}/preview/${master_id}`;
    copyText(href);
  };

  // 处理发布渠道详情点击
  const handleChannelDetail = (item: IAgentItem, channel: string) => {
    const { master_id } = item;
    // // 根据不同渠道跳转到不同的详情页面或执行不同操作
    switch (channel) {
      case 'website':
        // 网页版
        router.push(`/intelligent/${master_id}?tab=publish`);
        break;
      case 'embedded':
        // 网站嵌入
        router.push(`/intelligent/${master_id}?tab=publish`);
        break;
      default:
        break;
    }
  };

  watch(
    () => route.path,
    (path) => {
      if (path === '/intelligent') {
        fetchList();
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <template v-if="route.params.id">
    <router-view></router-view>
  </template>
  <a-spin v-else :spinning="spinning">
    <div class="flex justify-between w-100% m-b-20px pr-8px">
      <a-button type="primary" @click="handleAdd"><PlusOutlined />创建应用</a-button>
      <div class="flex">
        <a-input-search
          v-model:value="searchState.name"
          allow-clear
          placeholder="搜索应用名称"
          @change="debouncedSearch"
        />
        <a-select
          v-model:value="searchState.status"
          placeholder="发布状态"
          allow-clear
          style="width: 300px; padding-left: 10px"
          @change="onSearch"
        >
          <a-select-option value="archived">已发布</a-select-option>
          <a-select-option value="new">未发布</a-select-option>
        </a-select>
      </div>
    </div>
    <div ref="containerRef" class="box overflow-scroll">
      <div v-if="dataSource.length" class="w-100% max-h-100%">
        <div class="flex flex-wrap w-100% h-100%">
          <div v-for="item in dataSource" :key="item.id" class="item">
            <div class="header">
              <img :src="item.icon_url" alt="" />
              <div>
                <div class="text-18px p-b-5px">{{ item.name }}</div>
                <div class="inline">
                  <a-tag color="#E6E6E6" style="color: #797979">交互式智能体</a-tag>
                </div>
              </div>
              <span v-if="item.status === 'new'">
                <a-tag :color="STATUS_OPTIONS.find((opt) => opt.value === item.status)?.color" style="margin: 0">{{
                  STATUS_OPTIONS.find((opt) => opt.value === item.status)?.label
                }}</a-tag>
              </span>
              <a-popover v-else>
                <template #content>
                  <p>最近发布时间：{{ convertIsoTimeToLocalTime(item.updated_at) }}</p>
                  <p>发布渠道：网页版</p>
                  <p class="flex justify-between min-w-200px">
                    <a-button
                      type="link"
                      :disabled="item.status === 'new'"
                      style="padding: 0"
                      @click="handlePreview(item.master_id)"
                      >立即访问</a-button
                    >
                    <a-button
                      type="link"
                      :disabled="item.status === 'new'"
                      style="padding: 0"
                      @click="handleCopy(item.master_id)"
                      >复制智能体链接</a-button
                    >
                  </p>
                </template>

                <a-badge v-if="['published_failed', 'draft'].includes(item.status)" dot>
                  <!-- <span> -->
                  <a-tag :color="STATUS_OPTIONS.find((opt) => opt.value === item.status)?.color" style="margin: 0">{{
                    STATUS_OPTIONS.find((opt) => opt.value === item.status)?.label
                  }}</a-tag>
                  <!-- </span> -->
                </a-badge>
                <span v-else>
                  <a-tag :color="STATUS_OPTIONS.find((opt) => opt.value === item.status)?.color" style="margin: 0">{{
                    STATUS_OPTIONS.find((opt) => opt.value === item.status)?.label
                  }}</a-tag>
                </span>
              </a-popover>
            </div>
            <div class="content">
              <div>应用 ID：{{ item.id }}</div>
              <div>选用模型：{{ item.model_name || '--' }}</div>
              <div>
                发布渠道：
                <template v-if="item.publish_channel && item.publish_channel.length > 0">
                  <a-button
                    v-for="(channel, index) in item.publish_channel"
                    :key="index"
                    type="link"
                    size="small"
                    style="padding: 0 4px; height: auto"
                    @click="handleChannelDetail(item, channel)"
                  >
                    {{ channel === 'website' ? '网页版' : '嵌入版' }}
                  </a-button>
                </template>
                <span v-else>--</span>
              </div>
              <div class="flex">
                最近编辑：{{ convertIsoTimeToLocalTime(item.updated_at) }}
                <!-- <div v-if="['published_failed', 'draft'].includes(item.status)" class="text-red ml-20px">
                  此次编辑未发布
                </div> -->
              </div>
            </div>
            <div class="footer">
              <div
                class="footer-btn"
                @click="
                  router.push(
                    `/intelligent/${item.id}?status=${item.status}&publishTime=${encodeURIComponent(item.updated_at)}`,
                  )
                "
              >
                <EditOutlined /><span>编辑</span>
              </div>
              <div class="footer-btn" @click="handlePublish(item)">
                <!-- <svg class="footer-icon" aria-hidden="true">
                  <use xlink:href="#icon-fabu"></use>
                </svg> -->
                <FileTextOutlined />
                <span>发布</span>
              </div>
              <div class="footer-btn" @click="handleDelete(item)"><DeleteOutlined /><span>删除</span></div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="empty">
        <a-empty
          :description="
            deepEqual(searchState, DEFAULT_SEARCH_STATE) ? '你还没有创建应用哦，快去创建吧' : '暂无相关应用'
          "
        ></a-empty>
      </div>
    </div>
    <div v-if="dataSource.length" class="pagination">
      <a-pagination
        v-model:current="pagination.current"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :show-total="pagination.showTotal"
        :show-size-changer="false"
        @change="handleToggle"
      />
    </div>
  </a-spin>
  <a-modal v-model:open="delVisible" centered :title="`确定删除应用“${currentRecord.name}”？`" @ok="confirmDelete">
    <p>删除后不可恢复</p>
  </a-modal>
</template>

<style scoped lang="less">
  .box {
    height: calc(100% - 96px);
  }
  .item {
    width: calc(33.3% - 14px);
    height: 33%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 10px;
    border: 1px solid #ddd;
    padding: 10px;
    padding-bottom: 0;
    margin-bottom: 20px;
    &:nth-child(3n) {
      margin-right: 0;
    }
    &:nth-last-child(-n + 3) {
      margin-bottom: 0;
    }
    margin-right: 20px;
    .header {
      display: flex;
      width: 100%;
      > img {
        width: 60px;
        height: 60px;
        border-radius: 5px;
        border: none;
      }
      > span {
        max-width: 100px;
        border-radius: 5px;
      }
      > div {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0 10px;
      }
    }
    .content {
      margin-top: 5px;
      > div {
        padding-bottom: 10px;
        color: #797979;
      }
    }
    .footer {
      border-top: 1px solid #ddd;
      display: flex;
      .footer-btn {
        flex: 1;
        // padding: 10px 10px 0 10px;
        padding: 10px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 24px;
        color: #797979;
        position: relative;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
        > span {
          margin-left: 10px;
        }
        &::after {
          content: '';
          width: 1px;
          height: 60%;
          background: #ddd;
          position: absolute;
          left: 100%;
        }
        &:last-child {
          &::after {
            display: none;
          }
        }
      }
      .footer-icon {
        width: 16px;
        height: 16px;
      }
    }
  }
  .empty {
    height: calc(100% - 40px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .pagination {
    width: 100%;
    height: 32px;
    display: flex;
    justify-content: flex-end;
    margin-top: 12px;
  }
</style>
