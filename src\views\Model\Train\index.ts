import type { IOptions } from "@/interface";
import type { ITrainMethodsItem, ITrainTypesItem, ITrainFrameworksItem, IParameters } from "@/interface/model";

export const trainTypes: ITrainTypesItem[] = [
  {
    label: 'LoRA',
    value: 'lora',
    desc: 'LoRA训练属于高效训练的一种，会在固定模型本身参数的基础上，仅对自注意力权重矩阵进行低秩分解，并更新低秩矩阵参数。该训练方法训练时间短，但效果可能会略差于全参微调。',
  },
  // { label: 'QLoRA', value: 1, desc: 'QLoRA训练属于高效训练的一种，会在固定并量化模型本身参数的基础上，仅对自注意力权重矩阵进行低秩分解，并更新低秩矩阵参数。该训练方法训练时间短，但效果可能会略差于全参微调和LoRA。' },
  {
    label: 'full',
    value: 'full',
    desc: '全参微调会在模型训练中更新模型的全量参数，一般效果较好，但模型训练时间较长。',
  },
  {
    label: 'freeze',
    value: 'freeze',
    desc: '部分参数微调训练仅对模型中的部分参数进行更新，而非全量参数。相比于全参微调，它的计算量和训练时间通常会减少，训练效率更高。',
  },
];

export const trainMethods: ITrainMethodsItem[] = [
  {
    name: 'SFT 指令监督微调',
    value: 'sft',
    desc: '指令监督微调（Supervised Fine - tuning）是在预训练模型基础上，利用包含各种指令及对应期望输出的数据集对模型进行微调。这些指令涵盖多种自然语言处理任务，使模型学习理解不同指令含义并生成符合要求的输出，增强模型对特定任务指令的执行能力。',
    type: trainTypes,
  },
  // { name: 'RM 奖励模型训练', value: 1, desc: '奖励模型训练（Reward Model）是训练一个模型来评估生成结果的质量。依据生成文本的准确性、相关性、流畅性等多种标准，为模型生成的内容分配奖励分数，用于后续强化学习过程中衡量模型生成行为的优劣，引导模型优化。', type: trainTypes },
  // { name: 'PPO 近端策略优化训练', value: 2, desc: '近端策略优化训练（Proximal Policy Optimization）属于强化学习训练方式。它通过让模型与环境交互，根据奖励模型给出的奖励反馈来调整模型生成策略，在保证策略更新稳定性的同时，使模型不断学习生成能获得更高奖励的文本，提升模型性能。', type: trainTypes },
  // { name: 'DPO 直接偏好优化训练', value: 3, desc: '直接偏好优化训练（（Direct Preference Optimization））基于人类对不同生成结果的偏好数据，直接对模型参数进行优化，让模型学习生成更符合人类偏好的内容，提高生成文本与人类期望的契合度。', type: trainTypes },
  // { name: 'KTO 知识转移优化训练', value: 4, desc: '知识转移优化训练（Knowledge - Transfer Optimization）致力于将已有的知识（如其他预训练模型学到的知识）迁移到目标模型中，通过特定的方法和策略，使目标模型快速吸收和利用这些知识，提升在特定任务上的表现和泛化能力。', type: trainTypes },
  // { name: 'ORPO 知识转移优化训练', value: 5, desc: '离线强化策略优化训练（Offline Reinforcement Policy Optimization）是在不依赖实时环境交互的情况下，利用已有的离线数据进行强化学习策略的优化。通过对离线数据的分析和学习，调整模型策略，使模型在给定数据分布下达到更好的性能。', type: trainTypes },
  // { name: 'SimPO 简单策略优化训练', value: 6, desc: '简单策略优化训练（Simple Policy Optimization）采用相对简单的优化方法和策略，对模型的生成策略进行优化。在追求一定优化效果的同时，降低计算复杂度和训练成本，提高训练效率和可操作性。', type: trainTypes },
];

export const trainFrameworks: ITrainFrameworksItem[] = [
  {
    name: 'LLaMA-Factory',
    value: 'llama-factory',
    desc: '专为多 GPU 集群（如多卡 A100/H100）优化，提供完整的分布式训练框架，集成量化压缩，长上下文扩展等企业级功能。',
  },
  {
    name: 'Unsloth',
    value: 'unsloth',
    desc: '轻量型单 GPU 部署框架，专注单卡环境资源效率，通过智能显存调度与计算图优化，显著降低硬件配置门槛。',
  },
]
export const exampleDataSource = [{ input: 'input', output: 'output' }];

export const unsloth_parameters = [
  {
    label: 'val_size 验证集比例',
    value: 'val_size',
    desc: '验证集占全部样本的百分比。取值范围为 [0, 1]',
    type: 'inputNumber',
    min: 0,
    max: 1,
    step: 0.001,
    precision: 3,
    required: true,
  },
  {
    label: 'compute_type 计算类型',
    value: 'compute_type',
    desc: '使用混合精度训练的计算类型',
    type: 'select',
    required: true,
    option: [
      { label: 'bf16', value: 'bf16' },
      { label: 'fp16', value: 'fp16' },
    ],
  },
  {
    label: 'num_train_epochs 训练轮数',
    value: 'num_train_epochs',
    desc: '需要执行的训练总轮数。',
    type: 'inputNumber',
    required: true,
  },
  {
    label: 'learning_rate 学习率',
    value: 'learning_rate',
    desc: 'AdamW 优化器的初始学习率。',
    type: 'input',
    required: true,
  },
  {
    label: 'weight_decay 权重衰减率',
    value: 'weight_decay',
    type: 'inputNumber',
    min: 0,
    max: 1,
    step: 0.01,
    precision: 2,
    required: true,
  },
  {
    label: 'per_device_train_batch_size 批处理大小',
    value: 'per_device_train_batch_size',
    desc: '每个 GPU 处理的样本数量。取值范围为 [1, 1024]',
    type: 'inputNumber',
    min: 1,
    max: 1024,
    required: true,
  },
  {
    label: 'gradient_accumulation_steps 梯度累积',
    value: 'gradient_accumulation_steps',
    desc: '梯度累积的步数。取值范围为 [1, 1024]',
    type: 'inputNumber',
    min: 1,
    max: 1024,
    required: true,
  },
  {
    label: 'warmup_steps 预热步数',
    value: 'warmup_steps',
    desc: '学习率预热采用的步数。取值范围为 [0, 5000]',
    type: 'inputNumber',
    min: 0,
    max: 5000,
    required: true,
  },
  {
    label: 'lr_scheduler_type 学习率调节器',
    value: 'lr_scheduler_type',
    desc: '学习率调度器的名称。',
    type: 'select',
    required: true,
    option: [
      { label: 'linear', value: 'linear' },
      { label: 'cosine', value: 'cosine' },
      { label: 'cosine_with_restarts', value: 'cosine_with_restarts' },
      { label: 'polynomial', value: 'polynomial' },
      { label: 'constant', value: 'constant' },
      { label: 'constant_with_warmup', value: 'constant_with_warmup' },
      { label: 'inverse_sqrt', value: 'inverse_sqrt' },
      { label: 'reduce_lr_on_plateau', value: 'reduce_lr_on_plateau' },
      { label: 'cosine_with_min_Ir', value: 'cosine_with_min_Ir' },
      { label: 'warmup_stable_decay', value: 'warmup_stable_decay' },
    ],
  },
]

// 通用参数微调
export const general_parameters = [
  {
    label: 'learning_rate 学习率',
    value: 'learning_rate',
    desc: 'AdamW 优化器的初始学习率。',
    type: 'input',
    required: true,
  },
  {
    label: 'num_train_epochs 训练轮数',
    value: 'num_train_epochs',
    desc: '需要执行的训练总轮数。',
    type: 'inputNumber',
    required: true,
  },
  {
    label: 'max_grad_norm 最大梯度范数',
    value: 'max_grad_norm',
    desc: '用于梯度裁剪的范数。',
    type: 'inputNumber',
    required: true,
  },
  {
    label: 'max_samples 最大样本数',
    value: 'max_samples',
    desc: '每个数据集的最大样本数。',
    type: 'inputNumber',
    required: true,
  },
  {
    label: 'computed_type 计算类型',
    value: 'computed_type',
    desc: '使用混合精度训练的计算类型',
    type: 'select',
    required: true,
    option: [
      { label: 'bf16', value: 'bf16' },
      { label: 'fp16', value: 'fp16' },
      // { label: 'fp32', value: 'fp32' },
      // { label: 'pure_bf16', value: 'pure_bf16' },
    ],
  },
  {
    label: 'cutoff_len 截断长度',
    value: 'cutoff_len',
    desc: '输入序列分词后的最大长度。取值范围为 [1, 131072]',
    type: 'inputNumber',
    min: 4,
    max: 131072,
    required: true,
  },
  {
    label: 'per_device_train_batch_size 批处理大小',
    value: 'per_device_train_batch_size',
    desc: '每个 GPU 处理的样本数量。取值范围为 [1, 1024]',
    type: 'inputNumber',
    min: 1,
    max: 1024,
    required: true,
  },
  {
    label: 'gradient_accumulation_steps 梯度累积',
    value: 'gradient_accumulation_steps',
    desc: '梯度累积的步数。取值范围为 [1, 1024]',
    type: 'inputNumber',
    min: 1,
    max: 1024,
    required: true,
  },
  {
    label: 'val_size 验证集比例',
    value: 'val_size',
    desc: '验证集占全部样本的百分比。取值范围为 [0, 1]',
    type: 'inputNumber',
    min: 0,
    max: 1,
    step: 0.001,
    precision: 3,
    required: true,
  },
  {
    label: 'lr_scheduler_type 学习率调节器',
    value: 'lr_scheduler_type',
    desc: '学习率调度器的名称。',
    type: 'select',
    required: true,
    option: [
      { label: 'linear', value: 'linear' },
      { label: 'cosine', value: 'cosine' },
      { label: 'cosine_with_restarts', value: 'cosine_with_restarts' },
      { label: 'polynomial', value: 'polynomial' },
      { label: 'constant', value: 'constant' },
      { label: 'constant_with_warmup', value: 'constant_with_warmup' },
      { label: 'inverse_sqrt', value: 'inverse_sqrt' },
      { label: 'reduce_lr_on_plateau', value: 'reduce_lr_on_plateau' },
      { label: 'cosine_with_min_Ir', value: 'cosine_with_min_Ir' },
      { label: 'warmup_stable_decay', value: 'warmup_stable_decay' },
    ],
  },
];

// 其他参数设置
export const other_parameters = [
  {
    label: 'logging_steps 日志间隔',
    value: 'logging_steps',
    desc: '每两次日志输出间的更新步数。取值范围为 [1, 1000]',
    type: 'inputNumber',
    min: 1,
    max: 1000,
    required: true,
  },
  {
    label: 'save_steps 保存间隔',
    value: 'save_steps',
    desc: '每两次断点保存间的更新步数。取值范围为 [10, 5000]',
    type: 'inputNumber',
    min: 10,
    max: 5000,
    required: true,
  },
  {
    label: 'warmup_steps 预热步数',
    value: 'warmup_steps',
    desc: '学习率预热采用的步数。取值范围为 [0, 5000]',
    type: 'inputNumber',
    min: 0,
    max: 5000,
    required: true,
  },
  {
    label: 'neftune_noise_alpha NEFTune 噪声参数',
    value: 'neftune_noise_alpha',
    desc: '嵌入向量所添加的噪声大小。取值范围为 [0, 10]',
    type: 'inputNumber',
    min: 0,
    max: 10,
    step: 0.01,
    precision: 2,
    required: true,
  },
  // {
  //   label: 'extra_args 额外参数',
  //   value: 'extra_args',
  //   desc: '以 JSON 格式传递给训练器的额外参数。',
  //   type: 'input',
  //   required: true,
  // },
  {
    label: 'packing 序列打包',
    value: 'packing',
    desc: '将序列打包为等长样本。',
    type: 'checkbox',
  },
  {
    label: 'neat_packing 使用无污染打包',
    value: 'neat_packing',
    desc: '避免打包后的序列产生交叉注意力。',
    type: 'checkbox',
  },
  {
    label: 'train_on_prompt 学习提示词',
    value: 'train_on_prompt',
    desc: '不在提示词的部分添加掩码（仅适用于 SFT）。',
    type: 'checkbox',
  },
  {
    label: 'mask_history 不学习历史对话',
    value: 'mask_history',
    desc: '仅学习最后一轮对话（仅适用于 SFT）。',
    type: 'checkbox',
  },
  {
    label: 'resize_vocab 更改词表大小',
    value: 'resize_vocab',
    desc: '更改分词器词表和嵌入层的大小。',
    type: 'checkbox',
  },
  {
    label: 'use_llama_pro 使用 LLaMA Pro',
    value: 'use_llama_pro',
    desc: '仅训练块扩展后的参数。',
    type: 'checkbox',
  },
];

// 部分参数微调
export const partial_parameters = [
  {
    label: 'freeze_trainable_layers 可训练层数',
    value: 'freeze_trainable_layers',
    desc: '最末尾（+）/最前端（-）可训练隐藏层的数量。取值范围为 [-128, 128]',
    type: 'inputNumber',
    min: -128,
    max: 128,
    required: true,
  },
  {
    label: 'freeze_trainable_modules 可训练模块',
    value: 'freeze_trainable_modules',
    desc: '可训练模块的名称。使用英文逗号分隔多个名称。',
    type: 'input',
    required: true,
  },
  {
    label: 'galore_rank 额外模块',
    value: 'galore_rank',
    desc: '除隐藏层以外的可训练模块名称。使用英文逗号分隔多个名称。',
    type: 'input',
  },
];

// LoRA 参数设置
const LoRA_parameters = [
  {
    label: 'lora_rank LoRA 秩',
    value: 'lora_rank',
    desc: 'LoRA 矩阵的秩大小。取值范围为 [1, 1024]',
    type: 'inputNumber',
    min: 1,
    max: 1024,
    required: true,
  },
  {
    label: 'lora_alpha LoRA 缩放系数',
    value: 'lora_alpha',
    desc: 'LoRA 缩放系数大小。取值范围为 [1, 2048]',
    type: 'inputNumber',
    min: 1,
    max: 2048,
    required: true,
  },
  {
    label: 'lora_dropout LoRA 随机丢弃',
    value: 'lora_dropout',
    desc: 'LoRA 权重随机丢弃的概率。取值范围为 [0, 1]',
    type: 'inputNumber',
    min: 0,
    max: 1,
    step: 0.01,
    precision: 2,
    required: true,
  },
  // {
  //   label: 'loraplus_lr_ratio LoRA LoRA+ 学习率比例',
  //   value: 'loraplus_lr_ratio',
  //   desc: 'LoRA+ 中 B 矩阵的学习率倍数。取值范围为 [1, 64]',
  //   type: 'inputNumber',
  //   min: 0,
  //   max: 64,
  //   step: 0.01,
  //   precision: 2,
  //   required: true,
  // },
  {
    label: 'create_new_adapter 新建适配器',
    value: 'create_new_adapter',
    desc: '在现有的适配器上创建一个随机初始化后的新适配器。',
    type: 'checkbox',
  },
  {
    label: 'use_rslora 使用 rslora',
    value: 'use_rslora',
    desc: '对 LoRA 层使用秩稳定缩放方法。',
    type: 'checkbox',
  },
  {
    label: 'use_dora 使用 DoRA',
    value: 'use_dora',
    desc: '使用权重分解的 LoRA。',
    type: 'checkbox',
  },
  // {
  //   label: 'use_pissa 使用 PiSSA',
  //   value: 'use_pissa',
  //   desc: '使用 PiSSA 方法。',
  //   type: 'checkbox'
  // },
  {
    label: 'lora_target LoRA 作用模块',
    value: 'lora_target',
    desc: '应用 LoRA 的模块名称。使用英文逗号分隔多个名称。',
    type: 'input',
  },
  // {
  //   label: 'additional_target 附加模块',
  //   value: 'additional_target',
  //   desc: '除 LoRA 层以外的可训练模块名称。使用英文逗号分隔多个名称。',
  //   type: 'input',
  // },
];

const RLHF_parameters = [
  {
    label: 'pref_beta Beta 参数',
    value: 'pref_beta',
    desc: '损失函数中 beta 超参数大小。取值范围为 [0, 1]',
    type: 'inputNumber',
    min: 0,
    max: 1,
    step: 0.01,
    precision: 2,
    required: true,
  },
  {
    label: 'pref_ftx Ftx gamma',
    value: 'pref_ftx',
    desc: '损失函数中 SFT 损失的权重大小。取值范围为 [1, 10]',
    type: 'inputNumber',
    min: 0,
    max: 10,
    step: 0.01,
    precision: 2,
    required: true,
  },
  {
    label: 'pref_loss 损失类型',
    value: 'pref_loss',
    desc: '损失函数中 SFT 损失的权重大小。',
    type: 'select',
    required: true,
    option: [
      { label: 'sag', value: 'sigmoid' },
      { label: 'hinge', value: 'hinge' },
      { label: 'ipo', value: 'ipo' },
      { label: 'kto_pair', value: 'kto_pair' },
      { label: 'orpo', value: 'orpo' },
      { label: 'simpo', value: 'simpo' },
    ],
  },
  {
    label: 'ppo_score_norm 归一化分数',
    value: 'ppo_score_norm',
    desc: 'PPO 训练中归一化奖励分数。',
    type: 'checkbox',
  },
  {
    label: 'ppo_whiten_rewards 白化奖励',
    value: 'ppo_whiten_rewards',
    desc: 'PPO 训练中将奖励分数做白化处理。',
    type: 'checkbox',
  },
];
const GaLore_parameters = [
  {
    label: 'use_galore 使用 GaLore',
    value: 'use_galore',
    desc: '使用 GaLore 优化器。',
    type: 'checkbox',
  },
  {
    label: 'galore_rank GaLore 秩',
    value: 'galore_rank',
    desc: 'GaLore 梯度的秩大小。取值范围为 [1, 1024]',
    type: 'inputNumber',
    min: 1,
    max: 1024,
    required: true,
  },
  {
    label: 'galore_update_interval 更新间隔',
    value: 'galore_update_interval',
    desc: '相邻两次投影更新的步数。取值范围为 [1, 2048]',
    type: 'inputNumber',
    min: 1,
    max: 2048,
    required: true,
  },
  {
    label: 'galore_scale Block GaLore 缩放系数',
    value: 'galore_scale',
    desc: 'GaLore 缩放系数大小。取值范围为 [0, 100]',
    type: 'inputNumber',
    min: 0,
    max: 100,
    step: 0.1,
    precision: 1,
    required: true,
  },
  {
    label: 'galore_target GaLore 作用模块',
    value: 'galore_target',
    desc: '应用 GaLore 的模块名称。使用英文逗号分隔多个名称。',
    type: 'input',
  },
];

const BAdam_parameters = [
  {
    label: 'use_badam 使用 BAdam',
    value: 'use_badam',
    desc: '使用 BAdam 优化器。',
    type: 'checkbox',
  },
  {
    label: 'badam_mode BAdam 模式',
    value: 'badam_mode',
    desc: '使用 layer-wise 或 ratio-wise BAdam 优化器。',
    type: 'select',
    required: true,
    option: [
      { label: 'layer', value: 'layer' },
      { label: 'ratio', value: 'ratio' },
    ],
  },
  // {
  //   label: 'adam_switch_mode 切换策略',
  //   value: 'adam_switch_mode',
  //   desc: 'Layer-wise BAdam 优化器的块切换策略。',
  //   type: 'select',
  //   required: true,
  //   option: [
  //     { label: 'ascending', value: 'ascending' },
  //     { label: 'random', value: 'random' },
  //     { label: 'fixed', value: 'fixed' },
  //     { label: 'descending', value: 'descending' },
  //   ]
  // },
  {
    label: 'badam_switch_interval 切换频率',
    value: 'badam_switch_interval',
    desc: 'Layer-wise BAdam 优化器的块切换频率。取值范围为 [1, 1024]',
    type: 'inputNumber',
    min: 1,
    max: 1024,
    required: true,
  },
  {
    label: 'badam_update_ratio Block 更新比例',
    value: 'badam_update_ratio',
    desc: 'Ratio-wise BAdam 优化器的更新比例。取值范围为 [0, 1]',
    type: 'inputNumber',
    min: 0,
    max: 1,
    step: 0.01,
    precision: 2,
    required: true,
  },
];

export const parameter: any = {
  // 'general_parameters': general_parameters,
  other_parameters: other_parameters,
  partial_parameters: partial_parameters,
  LoRA_parameters: LoRA_parameters,
  RLHF_parameters: RLHF_parameters,
  GaLore_parameters: GaLore_parameters,
  BAdam_parameters: BAdam_parameters,
};


export const GPUList: IOptions[] = [
  { label: 'GPU0', value: 0 },
  { label: 'GPU1', value: 1 },
  { label: 'GPU2', value: 2 },
  { label: 'GPU3', value: 3 },
  { label: 'GPU4', value: 4 },
  { label: 'GPU5', value: 5 },
  { label: 'GPU6', value: 6 },
  { label: 'GPU7', value: 7 },
]

export const sklearn_svc_parameters: IParameters[] = [
  {
    label: 'C 误分类惩罚系数',
    value: 'c',
    desc: '越大对误分类样本惩罚越严格，控制模型对训练集拟合程度',
    type: 'inputNumber',
    min: 0.001,
    max: 1000,
    step: 0.001,
    precision: 3,
    default: 1,
    required: true,
  },
  {
    label: 'kernel 核函数',
    value: 'kernel',
    desc: '决定数据映射到高维空间的方式，不同核函数适用场景不同',
    type: 'select',
    required: true,
    default: 'rbf',
    option: [
      { label: 'linear', value: 'linear' },
      { label: 'rbf', value: 'rbf' },
      { label: 'poly', value: 'poly' },
      { label: 'sigmoid', value: 'polynomial' },
      { label: 'precomputed', value: 'precomputed' },
    ],
  },
  {
    label: 'gamma 核函数系数',
    value: 'gamma',
    desc: '影响核函数作用范围，scale 模式下为 1/(n_features * X.var()) ',
    type: 'select',
    required: true,
    default: 'scale',
    option: [
      { label: 'scale', value: 'scale' },
      { label: 'auto', value: 'auto' },
      { label: '数值', value: '数值' },
    ],
  },
  {
    label: 'degree 多项式核函数的次数',
    value: 'degree',
    desc: '控制多项式展开的复杂度，仅当 kernel=poly 时生效',
    type: 'inputNumber',
    min: 1,
    default: 3,
    required: false,
  },
]

export const sklearn_linearRegression_parameters: IParameters[] = [
  {
    label: 'penalty 正则化类型',
    value: 'penalty',
    desc: '注意 ： liblinear 仅支持 L1，newton-cg仅支持L2',
    type: 'select',
    option: [
      { label: 'L1', value: 'L1' },
      { label: 'L2', value: 'L2' },
      { label: 'elasticnet', value: 'elasticnet' },
    ],
    default: 'L1',
    required: false,
  },
  {
    label: 'C  正则化强度',
    value: 'c',
    desc: '越小惩罚越强，与正则化力度负相关',
    type: 'inputNumber',
    min: 0.001,
    max: 100,
    step: 0.001,
    precision: 3,
    default: 1,
    required: false,
  },
  {
    label: 'solver 优化算法',
    value: 'solver',
    desc: '注意 ： liblinear 仅支持 L1，newton-cg仅支持L2',
    type: 'select',
    required: true,
    default: 'lbfgs',
    option: [
      { label: 'liblinear', value: 'liblinear' },
      { label: 'lbfgs', value: 'lbfgs' },
      { label: 'newton-cg', value: 'newton-cg' },
      { label: 'sag', value: 'sag' },
      { label: 'saga', value: 'saga' },
    ],
  },
  {
    label: 'multi_class 多分类策略',
    value: 'multi_class',
    desc: '',
    type: 'select',
    required: true,
    default: 'auto',
    option: [
      { label: 'ovr', value: 'ovr' },
      { label: 'multinomial', value: 'multinomial' },
      { label: 'auto', value: 'auto' },
    ],
  },
  {
    label: 'max_iter 最大迭代次数',
    value: 'max_iter',
    desc: '迭代不收敛时可增大',
    type: 'inputNumber',
    min: 1,
    default: 100,
    required: false,
  },
]


export const sklearn_KMeans_parameters: IParameters[] = [
  {
    label: 'n_clusters 聚类簇数',
    value: 'n_clusters',
    desc: '需预先指定，决定最终簇的数量',
    type: 'inputNumber',
    min: 1,
    default: 8,
    required: true,
  },
  {
    label: 'init 初始化方法',
    value: 'init',
    desc: 'k-means++ 更智能，random 为随机初始化',
    type: 'select',
    required: true,
    default: 'k-means++',
    option: [
      { label: 'k-means++', value: 'k-means++' },
      { label: 'random', value: 'random' },
    ],
  },
  {
    label: 'max_iter 最大迭代次数',
    value: 'max_iter',
    desc: '算法收敛前最大迭代轮数',
    type: 'inputNumber',
    min: 1,
    default: 300,
    required: false,
  },
  {
    label: 'algorithm 优化算法',
    value: 'algorithm',
    desc: 'auto 自动选择，full 为经典EM算法',
    type: 'select',
    required: true,
    default: 'auto',
    option: [
      { label: 'auto', value: 'auto' },
      { label: 'full', value: 'full' },
      { label: 'elkan', value: 'elkan' },
    ],
  },
]
export const sklearn_svc_default = {
  c: 1,
  kernel: 'rbf',
  gamma: 'scale',
  degree: 3
}
export const sklearn_linearRegression_default = {
  c: 1,
  penalty: 'L2',
  solver: 'lbfgs',
  multi_class: 'auto',
  max_iter: 100
}
export const sklearn_KMeans_default = {
  n_clusters: 8,
  init: 'k-means++',
  algorithm: 'auto',
  max_iter: 300
}


export const sklearn_paramters: Record<string, { value: unknown, params: IParameters[] }> = {
  'sklearn-svc': {
    value: sklearn_svc_default,
    params: sklearn_svc_parameters
  },
  'sklearn-linearRegression': {
    value: [],
    params: []
  },
  'sklearn-KMeans': {
    value: sklearn_KMeans_default,
    params: sklearn_KMeans_parameters
  },
}

interface INumericFeatures {
  label: string;
  value: string;
}
export const svc_numeric_features_map: INumericFeatures[] = [
  { label: 'Label', value: 'Label' },
  { label: 'RevolvingUtilizationOfUnsecuredLines', value: 'RevolvingUtilizationOfUnsecuredLines' },
  { label: 'age', value: 'age' },
  { label: 'NumberOfTime30-59DaysPastDueNotWorse', value: 'NumberOfTime30-59DaysPastDueNotWorse' },
  { label: 'DebtRatio', value: 'DebtRatio' },
  { label: 'MonthlyIncome', value: 'MonthlyIncome' },
  { label: 'NumberOfOpenCreditLinesAndLoans', value: 'NumberOfOpenCreditLinesAndLoans' },
  { label: 'NumberOfTimes90DaysLate', value: 'NumberOfTimes90DaysLate' },
  { label: 'NumberRealEstateLoansOrLines', value: 'NumberRealEstateLoansOrLines' },
  { label: 'NumberOfTime60-89DaysPastDueNotWorse', value: 'NumberOfTime60-89DaysPastDueNotWorse' },
  { label: 'NumberOfDependents', value: 'NumberOfDependents' },
]

export const kmeans_numeric_features_map: INumericFeatures[] = [
  { label: 'species', value: 'species' },
  { label: 'sepal length', value: 'sepal length' },
  { label: 'sepal width', value: 'sepal width' },
  { label: 'petal length', value: 'petal length' },
  { label: 'petal width', value: 'petal width' },
]
export const linearRegression_numeric_features_map: INumericFeatures[] = [
  { label: 'age', value: 'age' },
  { label: 'sex', value: 'sex' },
  { label: 'bmi', value: 'bmi' },
  { label: 'bp', value: 'bp' },
  { label: 's1', value: 's1' },
  { label: 's2', value: 's2' },
  { label: 's3', value: 's3' },
  { label: 's4', value: 's4' },
  { label: 's5', value: 's5' },
  { label: 's6', value: 's6' },
  { label: 'target', value: 'target' },
]

export const sklearn_numeric_features_map: Record<string, INumericFeatures[]> = {
  'sklearn-svc': svc_numeric_features_map,
  'sklearn-linearRegression': linearRegression_numeric_features_map,
  'sklearn-KMeans': kmeans_numeric_features_map,
}