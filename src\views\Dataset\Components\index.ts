export const datasetTypes = [
  {
    label: '文本生成-SFT',
    value: 'sft',
    desc: '基于 Self - Instruct 方法构建，由一系列 “Instruction+（可选 Input）+Output” 组成的训练数据。',
  },
  {
    label: '模型蒸馏/评测',
    value: 'model-distillation',
    desc: '仅包含 Prompt 的数据，可用于模型蒸馏与评测，助力训练及验证模型效果。',
  },
  {
    label: '传统机器学习训练',
    value: 'legacy-ml',
    desc: '包含特征与标签的结构化数据，需满足完整性与一致性，支撑传统机器学习模型的训练和评估。',
  },
];
export const dataSourceOptions = [
  {
    label: '自研数据',
    value: 'self-developed',
  },
  {
    label: '第三方',
    value: 'third-party',
  },
  {
    label: '业务数据',
    value: 'business-data',
  },
];
export const DATASET_IMPORT_STATE_MAP = {
  IMPORTING: 'importing',
  IMPORTED: 'imported',
  FAILED: 'failed'
}
export const import_status = [
  { label: '导入成功', value: 'imported', color: 'green' },
  { label: '导入中', value: 'importing', color: 'orange' },
  { label: '导入失败', value: 'failed', color: 'red' },
]
export const DATASET_PUBLISH_STATE_MAP = {
  PUBLISHED: 'published',
  UNPUBLISHED: 'unpublished',
  FAILED: 'failed'
}
export const publish_status = [
  { label: '发布成功', value: 'published', color: 'green' },
  { label: '未发布', value: 'unpublished', color: 'orange' },
  { label: '发布失败', value: 'failed', color: 'red' },
]

export const exportFormatOptions = [
  { label: 'json', value: 'json' },
  { label: 'xlsx', value: 'xlsx' },
  { label: 'csv', value: 'csv' },
];