import type { IPage } from ".";

export interface IFetchMyDataset extends IPage {
  name?: string;
  type?: string;
  source?: string;
  import_status?: string;
  publish_status?: string;
}

export interface ICreateDataset {
  name: string;
  summary?: string;
  source?: string;
  type: string;
  file: FormData
}

export interface IDatasetItems {
  id: string;
  name: string;
  summary: string;
  type: string;
  source: string;
  data_quantity: number;
  file_size: number;
  import_status: string;
  creator_user_name: string;
  created_at: string;
  publish_status: string;
  published_at: string;
}

export interface IDatasetOverview {
  id: string;
  name: string;
  summary: string;
  type: string;
  source: string;
  data_quantity: number;
  file_size: number;
  import_status: string;
  publish_status: string;
  creator_user_name: string;
  created_at: string;
  fields: Fields[];
}

export interface Fields {
  field_name: string;
  field_type: string;
  field_description: string;
  required: boolean;
  is_read_only: boolean;
}

export type exportFormatType = 'json' | 'jsonl' | 'xlsx' | 'csv'