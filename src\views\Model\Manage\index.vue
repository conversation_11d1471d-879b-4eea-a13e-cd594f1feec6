<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
  import { CustomForm } from '@/components';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table/interface';
  import { useRoute, useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { serveModel, fetchModelList, publishModelV2, tagTreeList, deleteModelV2 } from '@/api/model';
  import { serviceStatus, modelFunStatus } from '@/utils/enum';
  import Info from './info.vue';
  import type { IFormItem } from '@/components/CustomForm/index.vue';
  import type { IPage } from '@/interface';
  import type { Dayjs } from 'dayjs';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import type { IModelState, ITag, IModelFormState } from '@/interface/model';

  const route = useRoute();
  const router = useRouter();
  const modelInfoRef = ref();
  const DEFAULT_FORM_STATE: Partial<IModelFormState> = {
    tags: [],
    name: '',
    size: '',
    ops: [],
    seq: 1,
    category: undefined,
    source_path: undefined,
    source_name: undefined,
    template: undefined,
    published_at: '',
    inference_engine: undefined,
    engine: undefined,
    tf: undefined,
    description: undefined,
    detail: '',
  };
  const state = reactive<IModelState>({
    modalLoading: false,
    visible: false,
    type: 'add',
    formState: JSON.parse(JSON.stringify(DEFAULT_FORM_STATE)),
    tagList: [],
    modelCodes: [],
  });

  const DEFAULT_SEARCHSTATE = {
    name: undefined,
    tag: undefined,
    operations: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const formConfig: (IFormItem & { mode?: string; itemStyle?: Record<string, string> })[] = [
    {
      field: 'name',
      type: 'input',
      label: '模型名称',
      placeholder: '请输入关键字',
    },
    {
      field: 'tag',
      type: 'input',
      label: '模型标签',
      placeholder: '请输入关键字',
    },
    {
      field: 'operations',
      type: 'select',
      label: '模型操作',
      placeholder: '下拉选择',
      mode: 'multiple',
      itemStyle: {
        background: '#ccc',
      },
      options: Object.keys(modelFunStatus).map((e: string) => ({ label: modelFunStatus[e].label, value: e })),
    },
  ];
  const columns: ColumnType[] = [
    { title: '模型名称', dataIndex: 'name', fixed: 'left' },
    { title: '基础模型', dataIndex: 'source_name', width: 200, fixed: 'left' },
    { title: '模型大小', dataIndex: 'size', width: 120 },
    { title: '模型标签', dataIndex: 'tags', width: 120, ellipsis: true },
    { title: '模型操作', dataIndex: 'ops', width: 120, ellipsis: true },
    { title: '模型路径', dataIndex: 'source_path' },
    { title: '模型来源', dataIndex: 'source_name' },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '更新时间', dataIndex: 'updated_at' },
    { title: '发布时间', dataIndex: 'published_at' },
    { title: '状态', dataIndex: 'status' },
    { title: '排序', dataIndex: 'seq', width: 60 },
    { title: '操作', dataIndex: 'operation', fixed: 'right' },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const loading = ref(false);
  const dataSource: Record<string, string>[] = reactive([]);
  const tableHeight = ref(0);

  const getDataList = async () => {
    loading.value = true;
    try {
      const { page, total, items } = (await fetchModelList({ ...pageParame, ...searchState })) as IPage & {
        total: number;
        items: Record<string, string>[];
      };
      dataSource.length = 0;
      dataSource.push(...items);
      Object.assign(pagination, { current: page, total: total });
      loading.value = false;
    } catch {
      loading.value = false;
    }
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    pageParame.page = 1;

    getDataList();
  };

  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(pageParame, { page: 1, limit: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getDataList();
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    getDataList();
  };

  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = (tableItem?.clientHeight as number) - 46 - 52;
  };

  const cancel = () => {
    state.visible = false;
    modelInfoRef.value.resetFormFields();
  };

  const getRandomColor = (index: number, type?: string) => {
    const presetColors = [
      'pink',
      'red',
      'orange',
      'green',
      'cyan',
      'blue',
      'purple',
      'magenta',
      'volcano',
      'gold',
      'lime',
      'geekblue',
    ];
    if (type == 'operations') return 'blue';
    const currentHour = new Date().getHours(); // 获取当前小时 (0-23)
    const colorIndex = (currentHour + index) % presetColors.length;
    return presetColors[colorIndex];
  };

  interface IModalEventItem {
    id: string;
    model_id: string;
    name: string;
    model_source_id: string;
    size: string;
    operations: string[];
    seq: number;
    exp_temp: string;
    published_at: Dayjs | string;
  }
  // 弹窗逻辑
  const modalEvent = (type: 'add' | 'edit' = 'add', item?: IModelFormState) => {
    state.type = type;
    state.visible = true;
    if (type == 'add') {
      state.formState = JSON.parse(JSON.stringify(DEFAULT_FORM_STATE));
    } else if (type == 'edit') {
      state.formState = item as IModelFormState;
    }
  };
  // 模型逻辑
  const modelEnvet = async (envetType: string = 'add', obj?: { operation?: string; oid: string }) => {
    switch (envetType) {
      case 'add':
      case 'edit':
        await modelInfoRef.value.submit();
        await modelInfoRef.value.resetFormFields();
        await getDataList();
        state.visible = false;
        break;
      case 'del':
        const res = await deleteModelV2(obj!.oid);
        if (res === null) {
          message.success('删除成功！');
          await getDataList();
        }
        break;
      case 'serve':
        {
          const res = await serveModel(obj as { operation: string; oid: string });
          if (res === null) {
            message.success(`模型${serviceStatus[obj!.operation!].label}成功！`);
            await getDataList();
          }
        }
        break;
      case 'publish':
        {
          await publishModelV2({ version_id: obj!.oid });
          message.success('发布成功');
          await getDataList();
        }
        break;

      default:
        break;
    }
  };

  const clickInfo = (n: { id: string }) => {
    router.push({
      path: '/model/manage/detail',
      query: { ...route.query, modelid: n.id },
    });
  };

  const getTagList = async () => {
    const list: ITag[] = await tagTreeList();
    list.map((n: { name: string; children: { name: string }[] }) => {
      if (n.children) {
        n.children = n.children.filter((e: { name: string }) => e.name);
      }
      // @ts-expect-error
      if (n.children.length) state.tagList.push({ id: n.name, ...n });
    });
  };

  onMounted(async () => {
    await getDataList();
    await nextTick();
    await getTagList();
    getTableHeight();
  });

  onUnmounted(() => {});
</script>

<template>
  <div class="manage">
    <CustomForm :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
    <div class="table-button">
      <a-button type="primary" @click="modalEvent()"> 添加模型 </a-button>
    </div>
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :row-class-name="(_record: Record<string, string>) => (_record.checked ? 'checked' : null)"
      :loading="loading"
      :scroll="{ y: tableHeight - 150, x: 'max-content' }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, record, text }">
        <div v-if="column.dataIndex === 'operation'" class="operation-box">
          <a @click="clickInfo(record)">查看</a>
          <a @click="modalEvent('edit', record)">编辑</a>

          <span v-if="record.status === 'draft'" class="mr-10px">
            <a-badge dot>
              <a-popconfirm :title="`确定发布这条数据吗?`" @confirm="modelEnvet('publish', { oid: record.id })">
                <a style="margin: 0">发布</a>
              </a-popconfirm>
            </a-badge>
          </span>

          <a-popconfirm title="确定删除这条数据吗?" @confirm="modelEnvet('del', { oid: record.id })">
            <a class="del-btn">删除</a>
          </a-popconfirm>
        </div>
        <div v-else-if="['tags', 'ops', 'model_path'].includes(column.dataIndex)">
          <a-tooltip placement="topLeft">
            <template #title>
              <div>
                {{
                  Array.isArray(text) ? text.map((e) => (e?.name ? e.name : modelFunStatus[e].label)).join(' ') : text
                }}
              </div>
            </template>
            <div v-if="column.dataIndex == 'model_path'">{{ text }}</div>
            <div v-else-if="Array.isArray(record[column.dataIndex]) && !record[column.dataIndex].length">--</div>
            <a-tag
              v-for="(n, i) in record[column.dataIndex]"
              v-else
              :key="i"
              :color="getRandomColor(i, column.dataIndex)"
              >{{ modelFunStatus?.[n]?.label || n?.name }}</a-tag
            >
          </a-tooltip>
        </div>
        <div v-else-if="column.dataIndex == 'status'">
          <a-tag :color="serviceStatus[text].color">{{ serviceStatus[text].label }}</a-tag>
        </div>
        <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="['published_at'].includes(column.dataIndex)">
          {{ text.split('T')[0] }}
        </div>

        <div v-else>{{ text || '--' }}</div>
      </template>
    </a-table>

    <!-- 新增 编辑 修改 -->
    <a-drawer
      :body-style="{ padding: '0 15px' }"
      :width="600"
      :title="`${state.type === 'add' ? '添加' : '编辑'}模型`"
      placement="right"
      :open="state.visible"
      @close="cancel"
    >
      <template #footer>
        <slot name="footer">
          <div class="flex w-100% flex-justify-end">
            <a-button @click="cancel">
              <span>取消</span>
            </a-button>
            <a-button
              type="primary"
              :loading="state.modalLoading"
              style="margin-left: 10px"
              @click="modelEnvet(state.type)"
            >
              <span>确定</span>
            </a-button>
          </div>
        </slot>
      </template>
      <Info
        ref="modelInfoRef"
        :visible="state.visible"
        :id="state.formState.id"
        :default="DEFAULT_FORM_STATE"
        :type="state.type"
        :tags="state.tagList"
      />
    </a-drawer>
  </div>
</template>

<style lang="css" scoped>
  .manage {
    height: 100%;
    overflow: hidden;
  }

  .table-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }

  :deep(.ant-table-cell-ellipsis > div) {
    overflow: hidden;
    text-overflow: ellipsis;

    > div {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; */
  }

  :deep(.checked) td {
    background-color: #6f84ee70;
  }
</style>
