<script setup lang="ts">
  import { getLocalItem } from '@/utils';
  import { administratorIds } from '@/utils/enum';
  import { ref, computed } from 'vue';
  import List from '@/views/Dataset/Components/list.vue';
  import { useRoute } from 'vue-router';
  const route = useRoute();
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const isAdministrator = computed(() => administratorIds.includes(userId));
</script>

<template>
  <template v-if="route.name === 'Dataset'">
    <template v-if="isAdministrator">
      <List />
    </template>
    <template v-else>111</template>
  </template>
  <template v-else>
    <router-view></router-view>
  </template>
</template>

<style scoped lang="less"></style>
