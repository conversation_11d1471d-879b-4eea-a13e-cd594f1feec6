<script setup lang="ts">
  import {
    featchDatasetOverview,
    featchDatasetItems,
    publishDataset,
    unpublishDataset,
    updateDatasetItems,
    deleteDataset,
    deleteDatasetItems,
    insertDatasetItems,
    exportDataset,
  } from '@/api/dataset';
  import type { Fields, IDatasetOverview, exportFormatType } from '@/interface/dateset';
  import { ref, onMounted, reactive, computed, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { convertIsoTimeToLocalTime, getLocalItem, scientificToDecimal } from '@/utils/common';
  import { datasetTypes, publish_status, exportFormatOptions } from '.';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { InfoCircleOutlined } from '@ant-design/icons-vue';
  import type { IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import { message } from 'ant-design-vue';
  import { administratorIds } from '@/utils/enum';
  const route = useRoute();
  const router = useRouter();
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const isAdministrator = computed(() => administratorIds.includes(userId));
  const activeKey = ref('data');
  const export_format = ref<exportFormatType>('json');
  const visible = ref(false);
  const delVisible = ref(false);
  const exportVisible = ref(false);
  const isInsert = ref(false);
  const dataLoading = ref(false);
  const currentIndex = ref(-1);
  const formRef = ref();
  // @ts-expect-error
  const detail = reactive<IDatasetOverview>({});
  const dataOverview = ref([]);
  const dataColumns = ref<ColumnType[]>([]);
  const data_pagination = reactive({ ...TABLE_PAGINATION });
  const data_page: IPage = reactive({ page: 1, limit: 10 });
  // const fields_pagination = reactive({ ...TABLE_PAGINATION });
  // const fields_page: IPage = reactive({ page: 1, limit: 10 });
  const currentDataItem = reactive<Record<string, string | number>>({});
  const resetDataItem = (data: Fields[]) => {
    const record: Record<string, string | number> = {};
    data.forEach((field) => {
      // @ts-expect-error
      record[field.field_name] = undefined;
    });
    Object.assign(currentDataItem, record);
  };
  const getOverview = async () => {
    const data: IDatasetOverview = await featchDatasetOverview(String(route.params.id));
    Object.assign(detail, data);
    if (data.fields) {
      resetDataItem(data.fields);
    }
  };
  const getItems = async () => {
    dataLoading.value = true;
    const data = await featchDatasetItems(String(route.params.id), data_page);
    dataOverview.value = data.items;
    Object.assign(data_pagination, { current: data.page, total: data.total_count });
    if (data.items && data.items.length) {
      const keys = Object.keys(data.items[0]);
      const cloumn: ColumnType[] = [];
      console.log(detail.fields);
      keys.forEach((key) => {
        const description = detail.fields.find((item) => item.field_name === key)?.field_description;
        // @ts-expect-error
        cloumn.push({ title: key, dataIndex: key, description });
      });
      // @ts-expect-error
      dataColumns.value = [...cloumn, { title: '操作', dataIndex: 'operation', width: '120px' }];
    }
    dataLoading.value = false;
  };
  const handleBack = () => {
    router.back();
  };
  const handleInsert = () => {
    visible.value = true;
    isInsert.value = true;
    if (detail.fields) {
      resetDataItem(detail.fields);
    }
  };
  const handlePublish = async () => {
    await publishDataset(String(route.params.id));
    message.success('发布成功');
    await getOverview();
  };
  // const handleUnPublish = async () => {
  //   await unpublishDataset(String(route.params.id));
  //   message.success('取消发布成功');
  //   await getOverview();
  // };
  const delDataItem = async (index: number) => {
    await deleteDatasetItems(String(route.params.id), index);
    message.success('已删除');
    await getItems();
  };

  const confirmDelete = async () => {
    await deleteDataset(detail.id);
    message.success('已删除');
    delVisible.value = false;
    router.push('/dataset');
  };
  const confirmExport = async () => {
    const data = await exportDataset(String(route.params.id), export_format.value);
    const { download_url } = data;
    window.open(download_url);
    exportVisible.value = false;
  };
  const editDataItem = (record: Record<string, string | number>, index: number) => {
    isInsert.value = false;
    visible.value = true;
    currentIndex.value = index;
    console.log(index);
    Object.assign(currentDataItem, record);
  };
  const handleComfirm = async () => {
    await formRef.value.validateFields();
    dataLoading.value = true;
    if (isInsert.value) {
      await insertDatasetItems(String(route.params.id), currentDataItem);
      message.success('已插入');
    } else {
      await updateDatasetItems(String(route.params.id), currentIndex.value, currentDataItem);
      message.success('已编辑');
    }
    dataLoading.value = false;
    visible.value = false;
    await getItems();
  };
  const toggleTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    Object.assign(data_pagination, { current, pageSize });
    Object.assign(data_page, { page: current, limit: pageSize });
    getItems();
  };
  onMounted(async () => {
    await getOverview();
    await getItems();
  });
</script>

<template>
  <div>
    <div class="header">
      <LeftOutlined @click="handleBack" />
      <di class="m-l-10px leading-20px">
        <span class="text-20px">{{ detail.name }}</span>
        <span class="text-#797979 text-12px ml-10px">{{ detail.id }}</span>
      </di>
    </div>
    <div class="overview mt-10px">
      <a-descriptions size="small" :column="5">
        <a-descriptions-item label="发布状态">
          <a-tag :color="publish_status.find((item) => item.value === detail.publish_status)?.color">{{
            publish_status.find((item) => item.value === detail.publish_status)?.label
          }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="数据量">{{ detail.data_quantity }}</a-descriptions-item>
        <a-descriptions-item label="数据类型">
          {{ datasetTypes.find((item) => item.value === detail.type)?.label }}</a-descriptions-item
        >
        <a-descriptions-item label="大小">{{ detail.file_size }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ convertIsoTimeToLocalTime(detail.created_at) }}</a-descriptions-item>
      </a-descriptions>
      <div class="flex justify-between">
        <div class="summary">数据集简介：{{ detail.summary || '--' }}</div>
        <div class="operate">
          <a-button
            v-if="activeKey === 'data'"
            :disabled="!isAdministrator || detail.publish_status === 'published'"
            @click="handleInsert"
            >插入</a-button
          >
          <a-button @click="exportVisible = true">导出</a-button>
          <a-button
            type="primary"
            :disabled="!isAdministrator || detail.publish_status === 'published'"
            @click="handlePublish"
            >发布</a-button
          >
          <a-button :disabled="!isAdministrator" danger @click="delVisible = true">删除</a-button>
        </div>
      </div>
    </div>
    <!-- <a-button @click="handleUnPublish">取消发布</a-button> -->
    <a-tabs v-model:activeKey="activeKey" type="card">
      <a-tab-pane key="data" tab="数据预览">
        <a-table
          :data-source="dataOverview"
          :columns="dataColumns"
          :loading="dataLoading"
          :pagination="data_pagination"
          :scroll="{ y: 490 }"
          @change="toggleTable"
        >
          <template #headerCell="{ column }">
            <template v-if="column.description">
              {{ column.title }}
              <a-popover title="" trigger="hover">
                <template #content>
                  {{ column.description }}
                </template>
                <InfoCircleOutlined />
              </a-popover>
            </template>
          </template>
          <template #bodyCell="{ column, record, text, index }">
            <div v-if="column.dataIndex === 'operation'" class="operation-box">
              <a @click="editDataItem(record, index)">编辑</a>
              <a-popconfirm title="确定删除该数据吗?" @confirm="delDataItem(index)">
                <a class="del-btn">删除</a>
              </a-popconfirm>
            </div>
            <div v-else>
              {{
                text === undefined || text === null ? '--' : typeof text === 'number' ? scientificToDecimal(text) : text
              }}
            </div>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="fields" tab="字段预览">Content of Tab Pane 2</a-tab-pane>
    </a-tabs>
  </div>
  <a-modal v-model:open="visible" width="50%" centered :title="isInsert ? '插入数据' : '编辑数据'" @ok="handleComfirm">
    <a-form ref="formRef" layout="vertical" autocomplete="off" :model="currentDataItem">
      <a-form-item
        v-for="item in detail.fields"
        :label="item.field_name"
        :name="item.field_name"
        :rules="[{ required: item.required }]"
      >
        <template v-if="['float', 'int'].includes(item.field_type)">
          <a-input-number
            v-model:value="currentDataItem[item.field_name]"
            style="width: 100%"
            :step="item.field_type === 'int' ? 1 : 0.00000000000001"
            placeholder="请输入"
          />
        </template>
        <template v-if="item.field_type === 'string'">
          <a-input v-model:value="currentDataItem[item.field_name]" style="width: 100%" placeholder="请输入" />
        </template>
      </a-form-item>
    </a-form>
  </a-modal>

  <a-modal v-model:open="delVisible" centered :title="`确定删除数据集“${detail.name}”？`" @ok="confirmDelete">
    <p>
      数据集已被用于模型训练或评测任务，删除可能导致任务失败或数据不一致，请确保相关任务已完成或备份数据后再进行操作。确定删除数据集吗？
    </p>
  </a-modal>

  <a-modal v-model:open="exportVisible" centered title="导出数据" @ok="confirmExport">
    <a-form>
      <a-form-item label="导出格式">
        <a-radio-group v-model:value="export_format" :options="exportFormatOptions" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">
  .header {
    display: flex;
  }
  .summary {
    width: 80%;
    overflow: hidden; /* 超出部分隐藏 */
    white-space: nowrap; /* 强制不换行 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .operate {
    button {
      margin: 0 5px;
    }
  }
</style>
